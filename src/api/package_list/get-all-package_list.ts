//OPTIMIZED
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { useRecoilValue } from 'recoil';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';
import {
  IGetPackageListFilter,
  IGetPackageListFilterState,
} from '@/store/filters/package_list';

export async function getAllPackageList(filter: IGetPackageListFilter) {
  const baseUrl = '/api/packages/list';
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);

  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch invoices');
  }
  const { data } = await response.json();
  return data;
}

type QueryFnType = typeof getAllPackageList;

export const useGetAllPackageListApi = (
  organization_id: number,
  config?: QueryConfigType<QueryFnType>
) => {
  const filter = useRecoilValue(IGetPackageListFilterState);

  const combinedFilter = { ...filter, organization_id };
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: ['get-all-package-list', combinedFilter],
    queryFn: () => getAllPackageList(combinedFilter),
    ...config,
  });
};
