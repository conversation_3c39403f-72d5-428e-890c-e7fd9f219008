import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

async function getSubscriptionInvoices(id: string) {
  const response = await fetch(`/api/subscription-invoices/${id}`, {
    method: 'GET',
  });
  if (!response.ok) {
    throw new Error(`Error: ${response.status}`);
  }
  const json = await response.json();

  return json.data;
}

type QueryFnType = typeof getSubscriptionInvoices;

export const useGetSubscriptionInvoicesQuery = (
  id: string,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry: false,
    queryKey: [queryKey.subscription.getinvoices],
    queryFn: () => getSubscriptionInvoices(id),

    ...config,
  });
};
