//OPTIMIZED
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { ToastMessages } from '@/constants/toast-messages';
import { MutationConfig, useMutation } from '@/lib/react-query';

const importTrxUsers = async (body: any) => {
  const response = await fetch(`/api/transactions/import/`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data?.message);

  return data;
};

type QueryFnType = typeof importTrxUsers;

export const useImportTrxMutation = (config?: MutationConfig<QueryFnType>) => {
  return useMutation({
    onError: (err: any) => {
      console.log('err in product is ', err);

      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },

    retry: false,
    mutationKey: [queryKey.products.import],
    mutationFn: importTrxUsers,
    ...config,
  });
};
