import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { IAllClientsFilterState } from '@/store/filters/clients';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

async function getAllClients(
  filter: Partial<IAllClientsFilterState> | undefined
) {
  const baseUrl = `/api/clients/search`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);

  const response = await fetch(apiUrl, {
    method: 'GET',
  });

  console.log('resposne', response);
  if (!response.ok) {
    const errorData = await response.json();

    throw new Error(errorData.message || 'Failed to fetch clients');
  }
  const { data } = await response.json();
  return data;
}

type QueryFnType = typeof getAllClients;

type options = QueryConfigType<QueryFnType>;

export const useGetAllClientsQuery = (
  filter?: Partial<IAllClientsFilterState> | undefined,
  uniqueKey?: string,
  config?: options
) => {
  // Get org_id from URL if not in filter
  const getOrgId = () => {
    if (filter?.org_id) return filter.org_id;
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      return params.get('organization_id')
        ? Number(params.get('organization_id'))
        : null;
    }
    return null;
  };
  const raw = localStorage.getItem('UserState');
  const data = raw ? JSON.parse(raw) : null;
  const org = data?.UserState?.organization;

  const finalFilter = {
    ...filter,
    org_id: filter?.org_id || getOrgId() || org?.id,
  };

  console.log('filter', filter);

  console.log('orgidddd', getOrgId());

  console.log('finalFilter', finalFilter);

  const finalQueryKey = uniqueKey
    ? [queryKey.client.getById, finalFilter, uniqueKey]
    : [queryKey.client.getById, finalFilter];

  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: finalQueryKey,
    queryFn: () => getAllClients(finalFilter),
    ...config,
  });
};
