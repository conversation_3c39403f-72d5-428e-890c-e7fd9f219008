import { query<PERSON><PERSON> } from '@/constants/query-key';
import { QueryConfigType, useQuery } from '@/lib/react-query';

async function getRefund() {
  const response = await fetch('/api/refunds', { method: 'GET' });
  const data = await response.json();
  return data;
}
type QueryFnType = typeof getRefund;

export const useGetRefundsQuery = (config?: QueryConfigType<QueryFnType>) => {
  return useQuery({
    retry(failureCount: any, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.refunds.getAll],
    queryFn: getRefund,
    ...config,
  });
};
