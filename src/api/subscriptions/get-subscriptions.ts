import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

async function getSubscriptions(id: string) {
  const response = await fetch(`/api/subscriptions/${id}`, { method: 'GET' });
  if (!response.ok) {
    throw new Error(`Error: ${response.status}`);
  }
  const json = await response.json();

  return json.data;
}

type QueryFnType = typeof getSubscriptions;

export const useGetSubscriptionsQuery = (
  id: string,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry: false,
    queryKey: [queryKey.subscription.get],
    queryFn: () => getSubscriptions(id),

    ...config,
  });
};
