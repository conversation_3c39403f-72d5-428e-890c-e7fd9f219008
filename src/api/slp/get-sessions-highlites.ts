import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

import { buildUrlWithQueryParams } from '@/utils/build-url-query';

// Define timezone as optional in the input type
interface GetSlpSessionsHighlitesData {
  slpId: number;
  year?: number; // Make it optional
  organization_id?: any;
}

async function getSlpSessionHighlites(data: GetSlpSessionsHighlitesData) {
  const baseUrl = '/api/users/slp/slp-highlites';
  const apiUrl = buildUrlWithQueryParams(baseUrl, data);
  const response = await fetch(apiUrl, { method: 'GET' });
  if (!response.ok) {
    throw new Error(`Error: ${response.status}`);
  }
  const json = await response.json();

  return json.data;
}

type QueryFnType = typeof getSlpSessionHighlites;

export const useGetSlpSessionHighLitesQuery = (
  data: GetSlpSessionsHighlitesData, // Updated type with optional timezone
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      if (failureCount < 1) return true;
      return false;
    },
    queryKey: ['get-slp-sessions-highlites', data?.year],
    queryFn: () => getSlpSessionHighlites(data), // Correct

    ...config,
  });
};
