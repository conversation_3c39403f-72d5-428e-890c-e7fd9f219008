import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const addRefund = async (body: any) => {
  const response = await fetch(`/api/refunds`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating slp note');
  }
  return response.json();
};

type QueryFnType = typeof addRefund;

export const useAddRefundApi = (config?: MutationConfig<QueryFnType>) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: ToastMessages.somethingWrong || err?.message,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Client refund data added',
      });
    },
    retry: false,
    mutationKey: ['add-refund'],
    mutationFn: addRefund,
    ...config,
  });
};
