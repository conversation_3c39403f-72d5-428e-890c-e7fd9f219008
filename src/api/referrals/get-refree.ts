import { QueryConfigType } from '@/lib/react-query';
import supabase from '@/lib/supabase/client';
import { useQuery } from '@supabase-cache-helpers/postgrest-react-query';
import { tableNames } from '@/constants/table_names';

export function getReferee(clientId: number) {
  return supabase
    .from(tableNames.referrals)
    .select('*, clients!referrals_referrer_id_fkey(*)')
    .eq('referee_id', clientId)
    .order('created_at', { ascending: false });
}

type QueryFnType = typeof getReferee;

export const useGetRefereeApi = (
  clientId: number,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery(getReferee(clientId), config);
};
