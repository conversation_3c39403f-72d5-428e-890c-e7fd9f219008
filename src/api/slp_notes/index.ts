/* eslint-disable no-constant-condition */

import { tableNames } from '@/constants/table_names';
// import supabase from '@/lib/supabase/client';

export const findAllSlpNotes = async (supabase: any) => {
  let offset = 0;
  let allData: any = [];
  const fetchLimit = 1000;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.slp_notes)
      .select('*')
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;
    if (data.length === 0) {
      break;
    }

    allData = allData.concat(data);

    offset += fetchLimit;
  }

  return allData;
};
