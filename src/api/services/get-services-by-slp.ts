//OPTIMIZED
import { query<PERSON>ey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

export async function getServicesBySlp(slp_id: string, config?: any) {
  // console.log('slp_id', slp_id);
  const response = await fetch(`/api/services/${slp_id}`, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch services');
  }
  const data = await response.json();
  if (config?.generateItemId) {
    const newServices = data?.services?.map((service: any) => {
      return {
        ...service,
        itemId: generateUUID(),
      };
    });
    return {
      ...data,
      services: newServices,
    };
  }
  return data;
}

type QueryFnType = typeof getServicesBySlp;

export const useGetServicesQuery = (
  id: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.services.getAll, Number(id)],
    queryFn: () => getServicesBySlp(id, config),
    ...config,
  });
};
