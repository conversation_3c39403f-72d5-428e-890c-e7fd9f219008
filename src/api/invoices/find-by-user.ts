//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

async function findByUser(data: {
  id: number;
  limit?: number;
  org_id: number;
}) {
  const baseUrl = `/api/invoices/user/${data.id}`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, {
    limit: data.limit,
    org_id: data.org_id,
  });
  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch invoices');
  }

  const res = await response.json();
  return res;
}

type QueryFnType = typeof findByUser;
type options = QueryConfigType<QueryFnType>;

export const useGetInvoicesByUserQuery = (
  data: { id: number; org_id: number; limit?: number },
  config?: options
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.invoices.getByUser, data],
    queryFn: () => findByUser(data),
    ...config,
  });
};
