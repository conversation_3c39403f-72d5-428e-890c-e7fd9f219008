import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

async function getSlpStatistics(status: string) {
  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const orgId = dataOrg?.UserState?.organization?.id;

  const baseUrl = '/api/users/slp/statistics';
  const apiUrl = buildUrlWithQueryParams(baseUrl, {
    status,
    organization_id: orgId, // Add organization_id to query params
  });

  const response = await fetch(apiUrl, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch statistics');
  }
  return await response.json();
}

type QueryFnType = typeof getSlpStatistics;
type options = QueryConfigType<QueryFnType>;

export const useGetSlpStatisticsQuery = (status: string, config?: options) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.slp.getStatistics, status],
    queryFn: () => getSlpStatistics(status),
    ...config,
  });
};
