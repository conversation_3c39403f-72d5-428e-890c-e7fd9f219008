//OPTIMIZED
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { useRecoilValue } from 'recoil';
import {
  IGetPackagesFilter,
  IGetPackagesFilterState,
} from '@/store/filters/packages';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

export async function getAllPackages(filter: IGetPackagesFilter) {
  const baseUrl = '/api/packages';
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);

  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch invoices');
  }
  const { data } = await response.json();
  return data;
}

type QueryFnType = typeof getAllPackages;

export const useGetAllPackagesApi = (config?: QueryConfigType<QueryFnType>) => {
  const filter = useRecoilValue(IGetPackagesFilterState);

  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: ['get-all-packages-raw', filter],
    queryFn: () => getAllPackages(filter),
    ...config,
  });
};
