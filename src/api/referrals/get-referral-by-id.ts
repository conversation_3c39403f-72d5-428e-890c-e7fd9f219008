import { queryKey } from '@/constants/query-key';
import { QueryConfigType, useQuery } from '@/lib/react-query';

const getRefereeByID = async (id: any) => {
  const response = await fetch(`/api/referrals/${id}/referee-id`, {
    method: 'GET',
  });
  const json = await response.json();
  if (!response.ok) {
    throw new Error(json.message || 'Failed to fetch referrals');
  }
  return json;
};

type QueryFnType = typeof getRefereeByID;

export const useGetRefereeByIDQuery = (
  id: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery({
    queryFn: () => getRefereeByID(id),
    queryKey: [queryKey.referrals.getRefereeById, id],
    ...config,
  });
};
