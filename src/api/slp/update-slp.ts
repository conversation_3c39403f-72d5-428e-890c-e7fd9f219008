//OPTIMIZED
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const updateSLP = async (body: { data: Partial<any>; id: number }) => {
  const response = await fetch(`/api/users/${body.id}`, {
    method: 'PATCH',
    body: JSON.stringify({ payload: body.data }),
  });
  if (!response.ok) throw new Error('Error fetching client data');
  return response.json();
};

type QueryFnType = typeof updateSLP;

export const useUpdateSLPMutation = (config?: MutationConfig<QueryFnType>) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Target hours updated successfully',
      });
    },
    retry: false,
    mutationKey: ['update-slp'],
    mutationFn: updateSLP,
    ...config,
  });
};
