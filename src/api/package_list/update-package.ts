//OPTIMIZED
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';

const updatePackage = async (body: { data: any; id: number }) => {
  const response = await fetch(`/api/packages/${body.id}`, {
    method: 'PATCH',
    body: JSON.stringify(body.data),
  });
  if (!response.ok) throw new Error('Error updating package');
  return response.json();
};

type QueryFnType = typeof updatePackage;

export const useUpdatePackageMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    // onSuccess: () => {
    //   toast({
    //     status: 'success',
    //     description: ToastMessages.packages.updateSuccess,
    //   });
    // },
    retry: false,
    mutationKey: [queryKey.packages.updatePackage],
    mutationFn: updatePackage,
    ...config,
  });
};
