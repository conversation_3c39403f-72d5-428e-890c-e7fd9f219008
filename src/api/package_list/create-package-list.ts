//OPTIMIZED
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';

const createPackageList = async (body: { data: any }) => {
  const response = await fetch(`/api/packages/list`, {
    method: 'POST',
    body: JSON.stringify(body.data),
  });
  if (!response.ok) throw new Error('Error creating package');
  return response.json();
};

type QueryFnType = typeof createPackageList;

export const useCreatePackageListMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    // onSuccess: () => {
    //   toast({
    //     status: 'success',
    //     description: ToastMessages.packages.updateSuccess,
    //   });
    // },
    retry: false,
    mutationKey: [queryKey.package_list.createPackageList],
    mutationFn: createPackageList,
    ...config,
  });
};
