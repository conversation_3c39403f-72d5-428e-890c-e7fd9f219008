import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const updateRefund = async (body: { data: any; id: number }) => {
  const response = await fetch(`/api/refunds/${body.id}`, {
    method: 'PATCH',
    body: JSON.stringify({ payload: body.data }),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating invoices data');
  }
  return response.json();
};

type QueryFnType = typeof updateRefund;

export const useUpdateRefundApi = (config?: MutationConfig<QueryFnType>) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Client refund data updated',
      });
    },
    retry: false,
    mutationKey: ['update-refund'],
    mutationFn: updateRefund,
    ...config,
  });
};
