import { queryKey } from '@/constants/query-key';
import { QueryConfigType, useQuery } from '@/lib/react-query';
import { IInvoices } from '@/shared/interface/invoice';

const getUnlinkedInvoicesById = async (id: number): Promise<IInvoices[]> => {
  const response = await fetch(`/api/referrals/unlinked-invoices/${id}`, {
    method: 'GET',
  });
  const json = await response.json();
  if (!response.ok) {
    throw new Error(json.message || 'Failed to fetch unlinked invoices');
  }
  return json;
};

type QueryFnType = typeof getUnlinkedInvoicesById;

export const useGetUnlinkedInvoiceQuery = (
  id: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<IInvoices[]>({
    queryFn: () => getUnlinkedInvoicesById(id),
    queryKey: [queryKey.referrals.getAllUnlinkedInvoices, id],
    ...config,
  });
};
