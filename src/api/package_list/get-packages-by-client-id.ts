//OPTIMIZED
import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

export async function getPackageByClientId(id: number) {
  const response = await fetch(`/api/packages/${id}`);
  if (!response.ok) throw new Error('Error fetching client data');
  return response.json();
}

type QueryFnType = typeof getPackageByClientId;

export const useGetPackagebyClientApi = (
  id: number,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.packages.getByClient, id],
    queryFn: () => getPackageByClientId(id),
    ...config,
  });
};
