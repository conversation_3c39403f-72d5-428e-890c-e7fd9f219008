import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const deleteRefund = async (body: any) => {
  const response = await fetch(`/api/refunds/${body}`, {
    method: 'DELETE',
    // body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating slp note');
  }
  return response.json();
};

type QueryFnType = typeof deleteRefund;

export const useDeleteRefundApi = (config?: MutationConfig<QueryFnType>) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Client data delete',
      });
    },
    retry: false,
    mutationKey: ['delete-refund'],
    mutationFn: deleteRefund,
    ...config,
  });
};
