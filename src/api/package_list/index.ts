/* eslint-disable no-constant-condition */
import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';

export const fetchAllPackagesIndex = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.packages)
      .select('*')
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }
    allData = allData.concat(data);
    offset += fetchLimit;
  }
  return allData;
};
export const fetchSlpPackagesIndex = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.invoices)
      .select('*')
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }
    allData = allData.concat(data);
    offset += fetchLimit;
  }
  return allData;
};

export const fetch50PackagesByTransactionDate = async () => {
  const { data, error, status } = await supabase
    .from('packages')
    .select(`*`)
    .limit(50)
    .order('transaction_dt', { ascending: false });

  if (error && status !== 406) {
    throw error;
  }

  return data;
};
