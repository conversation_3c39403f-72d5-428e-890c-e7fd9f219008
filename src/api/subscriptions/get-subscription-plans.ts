import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

async function getSubscriptionPlans() {
  const response = await fetch(`/api/subscription-plans`, { method: 'GET' });
  if (!response.ok) {
    throw new Error(`Error: ${response.status}`);
  }
  const json = await response.json();

  return json.data;
}

type QueryFnType = typeof getSubscriptionPlans;

export const useGetSubscriptionPlansQuery = (
  id: string,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry: false,
    queryKey: [queryKey.subscription.getPlans],
    queryFn: getSubscriptionPlans,

    ...config,
  });
};
