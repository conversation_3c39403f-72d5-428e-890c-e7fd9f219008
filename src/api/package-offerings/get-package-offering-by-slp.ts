//OPTIMIZED
import { query<PERSON><PERSON> } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

export async function getAllPackageOfferingsBySlp(
  filter: {
    user_id: string;
    organization_id?: string;
  },
  config?: any
) {
  const params = new URLSearchParams();
  if (filter.organization_id) {
    params.append('organization_id', filter.organization_id);
  }

  const response = await fetch(
    `/api/package-offering/${filter.user_id}?${params.toString()}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to fetch package offerings');
  }
  const data = await response.json();
  if (config?.generateItemId) {
    return data?.map((item: any) => {
      return {
        ...item,
        itemId: generateUUID(),
      };
    });
  }
  return data;
}

type QueryFnType = typeof getAllPackageOfferingsBySlp;

export const useGetPackageOfferingsQuery = (
  filter: {
    user_id: string;
    organization_id?: string;
  },
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [
      queryKey.packages.getPackageOfferings,
      filter.user_id,
      filter.organization_id,
    ],
    queryFn: () => getAllPackageOfferingsBySlp(filter, config),
    ...config,
  });
};
