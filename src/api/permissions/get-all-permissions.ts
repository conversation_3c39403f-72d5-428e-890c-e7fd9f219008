import supabase from '@/lib/supabase/client';
import { QueryConfigType } from '@/lib/react-query';
import { useQuery } from '@supabase-cache-helpers/postgrest-react-query';
import { tableNames } from '@/constants/table_names';

export const getAllPermissions = () => {
  const response = supabase.from(tableNames.permissions).select(`*`);
  return response;
};

type QueryFnType = typeof getAllPermissions;

export const useGetAllPermissions = (config?: QueryConfigType<QueryFnType>) => {
  return useQuery(getAllPermissions(), config);
};
