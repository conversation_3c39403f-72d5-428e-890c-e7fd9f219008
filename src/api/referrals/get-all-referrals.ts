import { queryKey } from '@/constants/query-key';
import { QueryConfigType, useQuery } from '@/lib/react-query';

const getReferrals = async (id: any) => {
  const response = await fetch(`/api/referrals/${id}`, { method: 'GET' });
  const json = await response.json();
  if (!response.ok) {
    throw new Error(json.message || 'Failed to fetch referrals');
  }
  return json;
};

type QueryFnType = typeof getReferrals;

export const useGetAllReferralsQuery = (
  id: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery({
    queryFn: () => getReferrals(id),
    queryKey: [queryKey.referrals.getAllReferrals, id],
    ...config,
  });
};
