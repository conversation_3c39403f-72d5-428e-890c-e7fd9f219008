import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

async function getUnlinkedSlpNotes(data: any) {
  const baseUrl = `/api/slp-notes`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, data || {});
  const response = await fetch(apiUrl, { method: 'GET' });
  const json = await response.json();
  return json;
}
type QueryFnType = typeof getUnlinkedSlpNotes;
export const useGetUnlinkedSoapNoteQuery = (
  data?: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.slpNotes.getAllSlpNotes, data],
    queryFn: () => getUnlinkedSlpNotes(data),
    ...config,
  });
};
