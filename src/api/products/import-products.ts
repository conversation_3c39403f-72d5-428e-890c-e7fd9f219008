//OPTIMIZED
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';

const importProducts = async (body: any) => {
  const response = await fetch(`/api/products/imports`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  const data = await response.json();
  if (!response.ok) throw new Error(data?.message);

  return data;
};

type QueryFnType = typeof importProducts;

export const useImportProductsMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      console.log('err in product is ', err);

      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },

    retry: false,
    mutationKey: [queryKey.products.import],
    mutationFn: importProducts,
    ...config,
  });
};
