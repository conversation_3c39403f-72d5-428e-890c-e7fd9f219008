//OPTIMIZED
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { queryKey } from '@/constants/query-key';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

export async function getAllPurchasedPackages(filter: any) {
  const baseUrl = `/api/purchases`;
  const url = buildUrlWithQueryParams(baseUrl, filter);
  console.log('url', url);
  const response = await fetch(url, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch purchased packages');
  }
  return await response.json();
}

type QueryFnType = typeof getAllPurchasedPackages;

export const useAllGetPurchasedPackagesQuery = (
  filter: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.packages.getAllPurchasedPackage, filter],
    queryFn: () => getAllPurchasedPackages(filter),
    ...config,
  });
};
