import { query<PERSON><PERSON> } from '@/constants/query-key';
import { QueryConfigType, useQuery } from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

async function getSupport(filter: any) {
  const baseUrl = `/api/support`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);
  const response = await fetch(apiUrl, { method: 'GET' });
  const data = await response.json();
  return data;
}
type QueryFnType = typeof getSupport;

export const useGetSupportsQuery = (
  filter: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery({
    retry(failureCount: any, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.support.getAll, filter],
    queryFn: () => getSupport(filter),
    ...config,
  });
};
