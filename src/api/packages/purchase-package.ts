//OPTIMIZED
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';

const purchasePackage = async (body: any) => {
  const response = await fetch(`/api/purchases`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) throw new Error('Error updating package');
  return response.json();
};

type QueryFnType = typeof purchasePackage;

export const usePurchasePackageMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    // onSuccess: () => {
    //   toast({
    //     status: 'success',
    //     description: ToastMessages.packages.updateSuccess,
    //   });
    // },
    retry: false,
    mutationKey: [queryKey.packages.purchasePackage],
    mutationFn: purchasePackage,
    ...config,
  });
};
