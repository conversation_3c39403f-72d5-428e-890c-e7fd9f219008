import { QueryConfigType } from '@/lib/react-query';
import supabase from '@/lib/supabase/client';
import { useQuery } from '@supabase-cache-helpers/postgrest-react-query';
import { tableNames } from '@/constants/table_names';

export function getReferral(clientId: number) {
  return supabase
    .from(tableNames.referrals)
    .select('*, clients!referrals_referee_id_fkey(*)')
    .eq('referrer_id', clientId)
    .order('created_at', { ascending: false });
}

type QueryFnType = typeof getReferral;

export const useGetReferralApi = (
  clientId: number,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery(getReferral(clientId), config);
};
