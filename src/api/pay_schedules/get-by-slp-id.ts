import { query<PERSON><PERSON> } from '@/constants/query-key';
import { ExtractFnReturnType, useQuery } from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

async function getPaySchedule(param: {
  slpId: number;
  year?: string | undefined;
}) {
  const baseUrl = `/api/pay-schedule/get-all`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, param);
  const response = await fetch(apiUrl, { method: 'GET' });
  const data = await response.json();

  return data;
}

type QueryFnType = typeof getPaySchedule;

export const useGetPayScheduleBySlpIdQuery = (
  param: {
    slpId: number;
    year?: any;
    org_id: number;
  },
  config?: ExtractFnReturnType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.paySchedule.getBySlpId, param],
    queryFn: () => getPaySchedule(param),
    ...config,
  });
};
