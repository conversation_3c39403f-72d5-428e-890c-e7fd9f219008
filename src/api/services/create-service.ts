import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const createService = async (body: any) => {
  const response = await fetch(`/api/services`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error creating service');
  }
  return response.json();
};
type MutationFnType = typeof createService;

export const useCreateServiceMutation = (
  config?: MutationConfig<MutationFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    retry: false,
    mutationKey: ['create-service'],
    mutationFn: createService,
    ...config,
  });
};
