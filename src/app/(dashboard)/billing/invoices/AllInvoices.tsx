/* eslint-disable react-hooks/exhaustive-deps */
'use client';
import { Badge, Box, Flex, HStack, Menu, Portal, Text } from '@chakra-ui/react';
import { useGetInvoicesHook } from '@/hooks/billing/invoice/useGetInvoicesHook';

import React, { useEffect, useState } from 'react';
// import { columnDef } from './columnDef';
import { useRecoilState } from 'recoil';
import AddInvoiceModal from './AddInvoiceModal';
import { IGetInvoicesFilterState } from '@/store/filters/invoices';
import CustomSelect from '@/components/Input/CustomSelect';
import { ReuseableInvoices } from '@/reuseables/invoice/Invoices';
import { Button } from '@/components/ui/button';
import { createInvoiceColumnDef } from '@/reuseables/invoice-column-def';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { useGetAllInvoicesQuery } from '@/api/newsf/queries';
//import { useRouter } from 'next/navigation';
import { LuFilter, LuX } from 'react-icons/lu';

type OptionType = {
  label: string;
  value: string | number;
};

// Define the filter state type
type FilterState = {
  sessionType: OptionType | null;
  slp: OptionType | null;
};

export default function AllInvoices() {
  const getInvoicesHook = useGetInvoicesHook();
  const [filter, setFilter] = useRecoilState(IGetInvoicesFilterState);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { UserFromQuery } = useSupabaseSession() as any;
  //const router = useRouter();
  const organizationId = UserFromQuery?.organization_id;

  // Add search state for non-org 1
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Helper function to get SLP object by ID
  const getSlpById = (slpId: number) => {
    return getInvoicesHook?.slpOptions?.find((slp: any) => slp.value === slpId);
  };

  // Local state for temporary filter values
  const [tempFilters, setTempFilters] = useState<FilterState>({
    sessionType: null,
    slp: null,
  });

  // State for applied filters display
  const [appliedFilters, setAppliedFilters] = useState<FilterState>({
    sessionType: null,
    slp: null,
  });

  // Update applied filters from current filter state on mount
  useEffect(() => {
    // Sync local state with global filter state when component mounts
    const sessionTypeFilter = filter.sessionType
      ? { label: filter.sessionType, value: filter.sessionType }
      : null;

    const slpFilter =
      filter?.selectedSlp && filter.selectedSlp.length > 0
        ? (() => {
            const slpObject = getSlpById(filter.selectedSlp[0]);
            return slpObject
              ? {
                  label: slpObject.label,
                  value: slpObject.value,
                }
              : null;
          })()
        : null;

    setAppliedFilters({
      sessionType: sessionTypeFilter,
      slp: slpFilter,
    });

    setTempFilters({
      sessionType: sessionTypeFilter,
      slp: slpFilter,
    });
  }, [filter]); // Run when filter changes

  // Build query parameters for non-org 1
  const buildQueryParams = () => {
    const params: any = {
      organization_id: organizationId,
    };

    // Add search parameter if there's a search term
    if (debouncedSearchTerm.trim()) {
      params.search = debouncedSearchTerm.trim();
    }

    // Add other filters as needed
    // You can add more filters here based on your requirements

    return params;
  };

  const { data: NewSfInvoices, isLoading: NewSfInvoicesLoading } =
    useGetAllInvoicesQuery(buildQueryParams(), {
      enabled: Boolean(organizationId),
    });

  // const { data: NewSfInvoices, isLoading: NewSfInvoicesLoading } =
  //   useGetAllInvoicesQuery(
  //     {
  //       organization_id: organizationId,
  //     },
  //     { enabled: Boolean(organizationId) }
  //   );

  console.log('NewSfInvoices is ', NewSfInvoices);

  const toggleSwitch = () => {
    setFilter((prevFilter) => ({
      ...prevFilter,
      page: 1,
      size: 50,
      currentPage: 1,
      linkedSlp: !prevFilter.linkedSlp,
    }));
  };

  // const handleRowClick = (
  //   row: any,
  //   event: React.MouseEvent<HTMLTableRowElement>
  // ) => {
  //   const target = event.target as HTMLElement;

  //   console.log('target is ', target);
  //   if (target.closest('[data-no-row-click="true"]')) {
  //     return;
  //   }

  //   if (organizationId !== 1) {
  //     return router.push(`/invoices/${row.original?.id}`);
  //   }
  // };

  // Handle apply filters
  const handleApplyFilters = () => {
    if (tempFilters.sessionType?.value) {
      getInvoicesHook?.handleSessionTypeChange(
        tempFilters.sessionType.value as string
      );
    }
    if (tempFilters.slp?.value) {
      getInvoicesHook.handleSlpChange(tempFilters.slp.value as number);
    }

    // Update applied filters for display
    setAppliedFilters({
      sessionType: tempFilters.sessionType,
      slp: tempFilters.slp,
    });

    setIsMenuOpen(false);
  };

  // Handle clear filters
  const handleClearFilters = () => {
    // Reset temp filters
    setTempFilters({
      sessionType: null,
      slp: null,
    });

    // Reset applied filters
    setAppliedFilters({
      sessionType: null,
      slp: null,
    });

    // Reset the actual filters in your hook - this is the key fix
    setFilter((prevFilter) => ({
      ...prevFilter,
      sessionType: undefined,
      selectedSlp: [],
      // Also reset any other filter properties that might be set
      search: '',
    }));

    // Also reset the search in the hook
    getInvoicesHook?.setSearch('');

    setIsMenuOpen(false);
  };

  // Remove individual filter
  const removeFilter = (filterType: string) => {
    const newAppliedFilters = { ...appliedFilters };
    const newTempFilters = { ...tempFilters };

    if (filterType === 'sessionType') {
      newAppliedFilters.sessionType = null;
      newTempFilters.sessionType = null;
      getInvoicesHook?.handleSessionTypeChange(null);
    } else if (filterType === 'slp') {
      newAppliedFilters.slp = null;
      newTempFilters.slp = null;
      getInvoicesHook?.handleSlpChange(null);
    }

    setAppliedFilters(newAppliedFilters);
    setTempFilters(newTempFilters);
  };

  // Count active filters
  const activeFiltersCount =
    Object.values(appliedFilters).filter(Boolean).length;

  return (
    <ReuseableInvoices
      data={
        organizationId === 1
          ? getInvoicesHook?.data || []
          : {
              invoices: NewSfInvoices?.data,
              total_count: NewSfInvoices?.pagination?.total_count,
            }
      }
      isLoading={
        organizationId === 1 ? getInvoicesHook?.isLoading : NewSfInvoicesLoading
      }
      section={'raw'}
      columnDef={createInvoiceColumnDef(
        organizationId ? parseInt(organizationId) : 1,
        'billing'
      )}
      isChecked={!filter?.linkedSlp}
      onChange={toggleSwitch}
      // search={getInvoicesHook.search}
      // setSearch={getInvoicesHook.setSearch}
      // onBlur={() =>
      //   getInvoicesHook.setFilter({
      //     ...getInvoicesHook.filter,
      //     search: getInvoicesHook.search,
      //   })
      // }
      search={organizationId === 1 ? getInvoicesHook.search : searchTerm}
      setSearch={
        organizationId === 1 ? getInvoicesHook.setSearch : setSearchTerm
      }
      onBlur={() => {
        if (organizationId === 1) {
          getInvoicesHook.setFilter({
            ...getInvoicesHook.filter,
            search: getInvoicesHook.search,
          });
        }
        // For non-org 1, the search is handled by the debounced effect
      }}
      addInvoiceModal={
        <Box
          width={{ base: 'full', md: 'auto' }}
          mt={{ base: '0', md: '0' }}
          //order={{ base: 1, md: 2 }} // Changed order here
          display={'flex'}
          justifyContent={'flex-end'}
        >
          <AddInvoiceModal
            variant={2}
            buttonName="New Invoice"
            refetch={getInvoicesHook?.invRefetch}
            org_id={UserFromQuery?.organization_id}
          />
        </Box>
      }
      options={
        <Flex
          direction={{ base: 'column', md: 'row' }}
          alignItems={{ base: 'flex-start', md: 'flex-end' }}
          gap={{ base: '1rem', md: '1rem' }}
          width="full"
          flexWrap="wrap"
          justifyContent="space-between"
          my={4}
        >
          {/* For mobile: Box comes first */}
          {/* <Box
            width={{ base: 'full', md: 'auto' }}
            mt={{ base: '0', md: '0' }}
            order={{ base: 1, md: 2 }} // Changed order here
            display={'flex'}
            justifyContent={'flex-end'}
          >
            <AddInvoiceModal
              variant={2}
              buttonName="New Invoice"
              refetch={getInvoicesHook?.invRefetch}
              org_id={UserFromQuery?.organization_id}
            />
          </Box> */}

          {/* For desktop: Flex comes first */}
          {/* <Flex
            mt={'4'}
            direction={{ base: 'row', md: 'row' }}
            gap={{ base: '1rem', md: '1rem' }}
            width={{ base: 'full', md: 'auto' }}
            order={{ base: 2, md: 1 }} // Changed order here
          >
            <Box
              width={{ base: 'full', md: '10rem' }}
              minWidth={{ base: 'unset', md: '10rem' }}
            >
              <CustomSelect
                placeholder="Select Session"
                label="Session Type"
                onChange={(val) =>
                  getInvoicesHook?.handleSessionTypeChange(val?.value)
                }
                options={getInvoicesHook?.sessionType}
              />
            </Box>

            <Flex
              width={{ base: 'full', md: '10rem' }}
              direction={{ base: 'column', md: 'row' }}
              minWidth={{ base: 'unset', md: '100%' }}
              gap={'10px'}
            >
              <Box minWidth={{ base: 'unset', md: '10rem' }}>
                <CustomSelect
                  placeholder="Select SLP"
                  label="SLP"
                  onChange={(val) => getInvoicesHook.handleSlpChange(val.value)}
                  options={getInvoicesHook?.slpOptions as any}
                />
              </Box>

              <Button
                bg="primary.500"
                _hover={{ bg: '#d96847' }}
                rounded={'md'}
                mt={{ base: '0', md: '1.9rem' }}
                width={{ base: 'full', md: '9rem' }}
                minWidth={{ base: 'unset', md: '9rem' }}
                onClick={getInvoicesHook.linkSlpToInvoice}
                loading={getInvoicesHook.linkLoading}
              >
                Link With SLP
              </Button>
            </Flex>
          </Flex> */}
          {/* Filter Section */}
          {organizationId === 1 && (
            <Flex
              direction={{ base: 'column', md: 'row' }}
              alignItems={{ base: 'flex-start', md: 'center' }}
              gap={{ base: '1rem', md: '1rem' }}
              order={{ base: 2, md: 1 }}
              flexWrap="wrap"
            >
              {/* Filter Menu */}
              <Menu.Root
                open={isMenuOpen}
                onOpenChange={({ open }) => setIsMenuOpen(open)}
              >
                <Menu.Trigger asChild>
                  <Button
                    variant="outline"
                    border="1px solid"
                    borderColor={'gray.50'}
                    fontSize={'md'}
                    _hover={{ bg: 'gray.50' }}
                    _active={{ bg: 'gray.50' }}
                    position="relative"
                  >
                    <LuFilter />
                    Filters
                    {activeFiltersCount > 0 && (
                      <Badge
                        colorScheme="red"
                        variant="solid"
                        fontSize="xs"
                        position="absolute"
                        top="-8px"
                        right="-8px"
                        borderRadius="full"
                        minW="20px"
                        h="20px"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                      >
                        {activeFiltersCount}
                      </Badge>
                    )}
                  </Button>
                </Menu.Trigger>

                <Portal>
                  <Menu.Positioner>
                    <Menu.Content
                      minW="20rem"
                      py={'6'}
                      px={'3'}
                      maxH="80vh"
                      overflow="visible"
                    >
                      <Text fontSize="lg" fontWeight="semibold" mb={4}>
                        Filter Options
                      </Text>

                      <Box mb={4}>
                        <CustomSelect
                          placeholder="Select Session"
                          label="Session Type"
                          value={tempFilters.sessionType}
                          onChange={(val: OptionType | null) =>
                            setTempFilters((prev) => ({
                              ...prev,
                              sessionType: val,
                            }))
                          }
                          options={getInvoicesHook?.sessionType}
                        />
                      </Box>

                      <Box mb={6}>
                        <CustomSelect
                          placeholder="Select SLP"
                          label="SLP"
                          value={tempFilters.slp}
                          onChange={(val: OptionType | null) =>
                            setTempFilters((prev) => ({ ...prev, slp: val }))
                          }
                          options={getInvoicesHook?.slpOptions as OptionType[]}
                        />
                      </Box>

                      {/* Action Buttons */}
                      <Flex gap={2} justifyContent="flex-end">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleClearFilters}
                          disabled={
                            !tempFilters.sessionType && !tempFilters.slp
                          }
                        >
                          Clear All
                        </Button>
                        <Button
                          bg="primary.500"
                          _hover={{ bg: '#d96847' }}
                          color="white"
                          size="sm"
                          onClick={handleApplyFilters}
                        >
                          Apply Filters
                        </Button>
                      </Flex>
                    </Menu.Content>
                  </Menu.Positioner>
                </Portal>
              </Menu.Root>
              {/* Current Filters Display */}
              {activeFiltersCount > 0 && (
                <HStack gap={2} flexWrap="wrap">
                  {appliedFilters.sessionType && (
                    <Badge
                      colorPalette="green"
                      variant="subtle"
                      display="flex"
                      alignItems="center"
                      gap={1}
                      px={2}
                      py={1}
                      borderRadius="md"
                    >
                      <Text fontSize="xs">
                        Session: {appliedFilters.sessionType.label}
                      </Text>
                      <Box
                        as="button"
                        cursor={'pointer'}
                        onClick={() => removeFilter('sessionType')}
                        ml={1}
                        _hover={{ opacity: 0.7 }}
                      >
                        <LuX size={12} />
                      </Box>
                    </Badge>
                  )}

                  {appliedFilters.slp && (
                    <Badge
                      colorPalette="purple"
                      variant="subtle"
                      display="flex"
                      alignItems="center"
                      gap={1}
                      px={2}
                      py={1}
                      borderRadius="md"
                    >
                      <Text fontSize="xs">SLP: {appliedFilters.slp.label}</Text>
                      <Box
                        as="button"
                        cursor={'pointer'}
                        onClick={() => removeFilter('slp')}
                        ml={1}
                        _hover={{ opacity: 0.7 }}
                      >
                        <LuX size={12} />
                      </Box>
                    </Badge>
                  )}
                </HStack>
              )}
            </Flex>
          )}
        </Flex>
      }
      //onRowClick={handleRowClick}
    />
  );
}
