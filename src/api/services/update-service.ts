import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const updateService = async (body: any) => {
  const response = await fetch(`/api/services`, {
    method: 'PATCH',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating service.');
  }
  return response.json();
};
type MutationFnType = typeof updateService;

export const useUpdateService = (config?: MutationConfig<MutationFnType>) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    retry: false,
    mutationKey: ['update-service'],
    mutationFn: updateService,
    ...config,
  });
};
