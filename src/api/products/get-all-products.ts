import { query<PERSON>ey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import {
  IGetServicesFilter,
  IGetServicesFilterState,
} from '@/store/filters/services';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';
import { useEffect, useState } from 'react';
import { useRecoilValue } from 'recoil';

// Custom hook to track current organization ID
function useCurrentOrgId(filter: IGetServicesFilter) {
  const [orgId, setOrgId] = useState<number | null>(null);

  useEffect(() => {
    const determineOrgId = () => {
      // 1. Check URL params first
      if (typeof window !== 'undefined') {
        const params = new URLSearchParams(window.location.search);
        const urlOrgId = params.get('organization_id');
        if (urlOrgId) return Number(urlOrgId);
      }

      // 2. Check localStorage organization
      const raw = localStorage.getItem('UserState');
      if (raw) {
        try {
          const data = JSON.parse(raw);
          const org = data?.UserState?.organization;
          if (org?.id) return org.id;
        } catch (e) {
          console.error('Error parsing UserState', e);
        }
      }

      // 3. Fall back to filter.org_id
      if (filter.org_id) return filter.org_id;

      return null;
    };

    const updateOrgId = () => {
      const newOrgId = determineOrgId();
      setOrgId((prev) => (prev !== newOrgId ? newOrgId : prev));
    };

    // Initial determination
    updateOrgId();

    // Listen for storage changes (organization changes in other tabs)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'UserState') {
        updateOrgId();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Cleanup
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [filter.org_id]);

  return orgId;
}

async function getAllProducts(filter: IGetServicesFilter, orgId: number) {
  const finalFilter = {
    ...filter,
    org_id: orgId, // Use the explicitly passed orgId
  };

  const baseUrl = '/api/products';
  const apiUrl = buildUrlWithQueryParams(baseUrl, finalFilter);
  const response = await fetch(apiUrl);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch products');
  }

  return await response.json();
}

type QueryFnType = typeof getAllProducts;

export const useGetAllProductsQuery = (
  config?: QueryConfigType<QueryFnType>
) => {
  const filter = useRecoilValue(IGetServicesFilterState);
  const orgId = useCurrentOrgId(filter);

  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.products.getAllProducts, filter, orgId],
    queryFn: () => {
      if (!orgId) {
        throw new Error('Organization ID is required to fetch products');
      }
      return getAllProducts(filter, orgId);
    },
    enabled: !!orgId, // Only enable query when orgId is available
    ...config,
  });
};
