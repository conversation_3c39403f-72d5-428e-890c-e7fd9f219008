import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

interface IPermissionRequest {
  id: number;
  permissions: any;
  role: string;
}

const updatePermissionsForUser = async (body: {
  data: Partial<IPermissionRequest>;
}) => {
  const { error: deleteError } = await supabase
    .from(tableNames.users)
    .update({ permissions_id: null })
    .eq('id', body.data.id);

  if (deleteError) {
    throw new Error(
      `Error deleting existing permissions: ${deleteError.message}`
    );
  }

  // Step 2: Update with new permissions
  const { data, error: upsertError } = await supabase
    .from(tableNames.users)
    .update({ permissions_id: body.data.permissions, role: body.data.role })
    .eq('id', body.data.id)
    .select();

  if (upsertError) {
    throw new Error(`Error upserting permissions: ${upsertError.message}`);
  }

  return data;
};

type QueryFnType = typeof updatePermissionsForUser;

export const useUpdateUserPermissionApi = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: ToastMessages.permissions.updateSuccess,
      });
    },
    retry: false,
    mutationKey: ['update-user-permission'],
    mutationFn: updatePermissionsForUser,
    ...config,
  });
};
