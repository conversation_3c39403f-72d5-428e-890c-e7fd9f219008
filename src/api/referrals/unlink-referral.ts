import { toaster } from '@/components/ui/toaster';
import { ToastMessages } from '@/constants/toast-messages';
import { MutationConfig, useMutation } from '@/lib/react-query';

const unlinkReferral = async (referralId: any) => {
  const response = await fetch(`/api/referrals/${referralId}/unlink`, {
    method: 'DELETE',
  });
  const json = await response.json();

  if (!response.ok) {
    throw new Error(json.error || 'Failed to unlink referral');
  }

  return json;
};

type QueryFnType = typeof unlinkReferral;

export const useUnlinkReferralMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: () => {
      toaster.create({ type: 'error', description: 'An error occurred' });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: ToastMessages.referrals.unlinkSuccess,
      });
    },
    retry: false,
    mutationKey: ['unlink-referral'],
    mutationFn: unlinkReferral,
    ...config,
  });
};
