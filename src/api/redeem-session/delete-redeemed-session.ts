import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';

const deleteRedeemedSession = async (session_id: any) => {
  const response = await fetch(`/api/redeemed-sessions/${session_id}`, {
    method: 'DELETE',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error deleting session');
  }
  return response.json();
};

type QueryFnType = typeof deleteRedeemedSession;

export const useDeleteRedeemedSessionApi = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'Session deleted successfully',
      });
    },
    retry: false,
    mutationKey: [queryKey.redeemSession.deleteRedeemedSession],
    mutationFn: deleteRedeemedSession,
    ...config,
  });
};
