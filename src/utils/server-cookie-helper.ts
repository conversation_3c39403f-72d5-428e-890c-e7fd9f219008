// lib/server/getOrganizationName.ts
import { cookies } from 'next/headers';

export function getOrganizationName(): string {
  const cookieValue = cookies().get('user_data')?.value;

  if (!cookieValue) return 'Soap';

  try {
    const parsed = JSON.parse(cookieValue);
    return parsed?.organization?.name || 'Soap';
  } catch (error) {
    console.error('Failed to parse user_data cookie:', error);
    return 'Soap';
  }
}
export function getOrganization(): any {
  const cookieValue = cookies().get('user_data')?.value;

  if (!cookieValue) return 'Soap';

  try {
    const parsed = JSON.parse(cookieValue);
    return parsed?.organization || {};
  } catch (error) {
    console.error('Failed to parse user_data cookie:', error);
  }
}
export function getUserFromCookie(): any {
  const cookieValue = cookies().get('user_data')?.value;

  if (!cookieValue) return 'Soap';

  try {
    const parsed = JSON.parse(cookieValue);
    return parsed || {};
  } catch (error) {
    console.error('Failed to parse user_data cookie:', error);
  }
}
