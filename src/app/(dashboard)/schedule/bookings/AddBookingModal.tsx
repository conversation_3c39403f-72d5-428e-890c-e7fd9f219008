import { CustomModal } from '@/components/elements/modal/custom-modal';
import { Button } from '@/components/ui/button';
import { Box, Flex, Stack, Text, useDisclosure } from '@chakra-ui/react';
// import ExistingUserForm from './ExistingUserForm';
// import NewUserForm from './NewUserForm';
import CTABtn from '@/components/elements/CTABtn';
// import SearchContact from '@/components/elements/search/SearchContact';
import CustomSelect from '@/components/Input/CustomSelect';
import StringInput from '@/components/Input/StringInput';
import { provinceOptions } from '@/data/options/consultations';
import { useAddBookingHook } from '@/hooks/schedule/bookings/useAddBookingHook';
import SelectContact from '@/components/elements/search/SelectContact';

export default function AddBookingModal({
  refetch,
  data,
  buttonName,
  variant = 1,
}: any) {
  const {
    open,
    onClose: bookingOnClose,
    onOpen: bookingOnOpen,
  } = useDisclosure();
  const {
    values,
    handleFormSubmit,
    handleChange,
    setFieldValue,
    SlpOptions,
    slpLoading,
    loading,
    servicesOption,
    errors,
    touched,
    isExistingClient,
    setIsExistingClient,
    showSearch,
    handleSelectClient,
    // searchResult,
    // setSearchResult,
    resetForm,
    setShowSearch,
  } = useAddBookingHook({ bookingOnClose, refetch, data });
  const isExisting = isExistingClient?.toLowerCase() === 'yes';

  console.log('values', values);
  console.log('isExisting', isExisting);

  return (
    <div>
      <Box>
        {/* <Button
          bg={'primary.500'}
          onClick={bookingOnOpen}
          mb={'2rem'}
          minW={'10rem'}
        >
          {buttonName || 'Add Booking'}
        </Button> */}
        <CTABtn
          variant={variant}
          buttonName={buttonName}
          onClick={bookingOnOpen}
        />
      </Box>
      <CustomModal
        w={{ base: '90%', md: '40rem' }}
        open={open}
        onOpenChange={() => {
          bookingOnClose();
          resetForm();
          setShowSearch(true);
        }}
      >
        <Box my={'1rem'}>
          <Text fontSize={'1.2rem'} textAlign={'center'} fontWeight={'bold'}>
            Create a New Booking
          </Text>
          {/* <Text textAlign={'center'}>
            Create a new booking where the booking is missing in database
          </Text> */}
        </Box>
        <Flex alignItems={'center'} gap={'2rem'}>
          <Text
            onClick={() => setIsExistingClient('yes')}
            cursor={'pointer'}
            borderBottom={'2px solid'}
            borderColor={isExisting ? 'primary.500' : 'transparent'}
            pb={'.2rem'}
            color={isExisting ? 'primary.500' : 'black'}
            fontWeight={isExisting ? '500' : '400'}
            px={'.2rem'}
          >
            Existing Client
          </Text>
          <Text
            cursor={'pointer'}
            onClick={() => setIsExistingClient('no')}
            borderBottom={'2px solid'}
            borderColor={!isExisting ? 'primary.500' : 'transparent'}
            pb={'.2rem'}
            color={!isExisting ? 'primary.500' : 'black'}
            fontWeight={!isExisting ? '500' : '400'}
            px={'.2rem'}
          >
            New Client
          </Text>
        </Flex>
        <Box>
          <form onSubmit={handleFormSubmit}>
            <Stack gap={'1rem'} pt={'1rem'}>
              {!isExisting && (
                <>
                  <StringInput
                    inputProps={{
                      name: 'first_name',
                      value: values.first_name || '',
                      onChange: handleChange,
                    }}
                    fieldProps={{
                      label: 'First name',
                      invalid: touched.first_name && !!errors.first_name,
                      errorText: errors.first_name as string,
                      required: true,
                    }}
                  />
                  <StringInput
                    inputProps={{
                      name: 'last_name',
                      value: values.last_name || '',
                      onChange: handleChange,
                    }}
                    fieldProps={{
                      label: 'Last name',
                      invalid: touched.last_name && !!errors.last_name,
                      errorText: errors.last_name as string,
                      required: true,
                    }}
                  />
                  <StringInput
                    inputProps={{
                      name: 'email',
                      value: values.email || '',
                      onChange: handleChange,
                    }}
                    fieldProps={{
                      label: 'Email',
                      invalid: touched.email && !!errors.email,
                      errorText: errors.email as string,
                      required: true,
                    }}
                  />
                  <StringInput
                    inputProps={{
                      name: 'phone',
                      value: values.phone || '',
                      onChange: handleChange,
                    }}
                    fieldProps={{
                      label: 'Phone',
                      invalid: touched.phone && !!errors.phone,
                      errorText: errors.phone as string,
                    }}
                  />

                  <CustomSelect
                    options={provinceOptions}
                    onChange={(e: any) => {
                      setFieldValue('province', e.value);
                    }}
                    label={'Province'}
                    name={'province'}
                    required={true}
                    // errors={errors}
                    // touched={touched}
                  />
                </>
              )}
              {isExisting && showSearch && !data && (
                <>
                  {/* <label className="font-medium text-gray-900">
                    Lookup Client
                  </label> */}
                  <SelectContact
                    label={'Client'}
                    handleSelectClient={handleSelectClient}
                  />
                  {/* <SearchContact
                    setSearchResult={(e: any) => {
                      setSearchResult(e);
                    }}
                    searchResult={searchResult}
                    selectExistingUser={(item) => handleSelectClient(item)}
                  /> */}
                </>
              )}
              {!showSearch && isExisting && !data && (
                <StringInput
                  inputProps={{
                    name: 'display_name',
                    value: values.display_name || '',
                    onChange: handleChange,
                  }}
                  fieldProps={{ label: 'Display name', disabled: true }}
                />
              )}
              <CustomSelect
                options={servicesOption}
                onChange={(e: any) => {
                  setFieldValue('event', e.value.name);
                  setFieldValue('service_id', e.value.id);
                }}
                defaultValue={servicesOption[0]}
                label={'Service'}
                // showRequired={true}
                // required={true}
                name={'event'}
                // errors={errors}
                // touched={touched}
              />

              {!slpLoading && (
                <CustomSelect
                  options={SlpOptions}
                  onChange={(e: any) => {
                    setFieldValue('assigned_to', e.value);
                  }}
                  selectedOption={
                    SlpOptions.length === 1 ? SlpOptions[0] : undefined
                  }
                  label={'Assigned To'}
                  name={'assigned_to'}
                  required={true}
                />
              )}

              <StringInput
                inputProps={{
                  type: 'datetime-local',

                  name: 'appointment',
                  value: values.appointment || '',
                  onChange: handleChange,
                }}
                fieldProps={{
                  label: 'Appointment Time',
                  required: true,
                  invalid: touched.appointment && !!errors.appointment,
                  errorText: errors.appointment as string,
                }}
              />

              <Flex
                my={'1.8rem'}
                alignItems={'center'}
                justifyContent={'space-between'}
              >
                <Button
                  onClick={bookingOnClose}
                  variant={'outline'}
                  minH={'3rem'}
                  minW={'15rem'}
                >
                  Cancel
                </Button>
                <Button
                  loading={loading}
                  minH={'3rem'}
                  minW={'15rem'}
                  bg={'primary.500'}
                  type="submit"
                >
                  Save
                </Button>
              </Flex>
            </Stack>
          </form>
        </Box>
      </CustomModal>
    </div>
  );
}

{
  /* {!data ? (
  <Box>
    <Tabs.Root
      border={'none'}
      defaultValue={'existing'}
      lazyMount
      size={{ base: 'sm', md: 'md' }}
    >
      <Tabs.List border={'none'}>
        <Tabs.Trigger
          value={'existing'}
          _selected={{ color: 'primary.500' }}
          _before={{ bg: 'primary.500' }}
          fontSize={{ base: '2xs', md: 'md' }}
        >
          Existing client
        </Tabs.Trigger>
        <Tabs.Trigger
          value={'new'}
          _selected={{ color: 'primary.500' }}
          _before={{ bg: 'primary.500' }}
          fontSize={{ base: '2xs', md: 'md' }}
        >
          New client
        </Tabs.Trigger>
      </Tabs.List>

      <Tabs.Content value={'existing'} pt={'3'}>
        <ExistingUserForm
          refetch={refetch}
          bookingOnClose={bookingOnClose}
        />
      </Tabs.Content>

      <Tabs.Content value={'new'} pt={'3'}>
        <NewUserForm
          refetch={refetch}
          bookingOnClose={bookingOnClose}
        />
      </Tabs.Content>
    </Tabs.Root>
  </Box>
) : (
  <ExistingUserForm
    refetch={refetch}
    data={data}
    bookingOnClose={bookingOnClose}
  />
)} */
}
