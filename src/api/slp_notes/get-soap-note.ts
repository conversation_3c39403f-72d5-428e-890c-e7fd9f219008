// let query = supabase
//     .from(vf_schedules)
//     .select(`*`)
//     .eq('slp_id', param.slpId);

//   if (param.year) {
//     query = query.eq('year', param.year);
//   }

import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

import { buildUrlWithQueryParams } from '@/utils/build-url-query';

export async function getSlpNotes(dto: {
  clientId: number;
  linkedClientId?: number;
}) {
  const baseUrl = `/api/slp-notes/${dto.clientId}`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, {
    linkedClientId: dto?.linkedClientId,
  });
  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  const json = await response.json();
  return json;
}
type QueryFnType = typeof getSlpNotes;
export const useGetSoapNoteQuery = (
  dto: {
    clientId: number;
    linkedClientId?: number;
  },
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.slpNotes.getByClientId, dto],
    queryFn: () => getSlpNotes(dto),
    ...config,
  });
};
