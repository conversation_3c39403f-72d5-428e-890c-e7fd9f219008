//OPTIMIZED
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

export async function getClientPackageProducts() {
  const response = await fetch('/api/package-transactions', {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(
      errorData.message || 'Failed to fetch package transactions'
    );
  }
  const data = await response.json();
  return data;
}

type QueryFnType = typeof getClientPackageProducts;

export const useGetPackageTransactionsQuery = (
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: ['get-all-packages-transactions'],
    queryFn: getClientPackageProducts,
    ...config,
  });
};
