import { useGetAllClientsQuery } from '@/api/clients/get-all-clients';
import CustomSelect from '@/components/Input/CustomSelect';
import { Box } from '@chakra-ui/react';
import React, { useMemo } from 'react';

const SelectContact = ({ label, handleSelectClient }: any) => {
  const { data: Clients } = useGetAllClientsQuery({}, 'link-client', {
    enabled: true,
  });
  const contactOptions = useMemo(() => {
    return Clients?.data?.map((item: any) => ({
      label: item?.display_name,
      value: item,
    }));
  }, [Clients]);

  console.log('clients', Clients);

  return (
    <div>
      <Box>
        <CustomSelect
          placeholder="Select Client"
          options={contactOptions}
          onChange={(option: any) => {
            handleSelectClient(option.value);
          }}
          label={label}
          // defaultValue={contactOptions?.find(
          //   (item: any) =>
          //     // item.value.toLowerCase() === values?.contact.toLowerCase()
          // )}
        />
      </Box>
    </div>
  );
};

export default SelectContact;
