//OPTIMIZED
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { queryKey } from '@/constants/query-key';

export async function getPurchasedPackages(client_id: string) {
  const response = await fetch(`/api/purchases/${client_id}`, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch purchased packages');
  }
  return await response.json();
}

type QueryFnType = typeof getPurchasedPackages;

export const useGetPurchasedPackagesQuery = (
  id: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.packages.getByClient, Number(id)],
    queryFn: () => getPurchasedPackages(id),
    ...config,
  });
};
