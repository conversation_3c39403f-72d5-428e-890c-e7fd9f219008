import { env } from '@/constants/env';
// import { tableNames } from '@/constants/table_names';
// import { addTimeToDate } from '@/utils/date-formatter';
// import { createServerClient } from '@supabase/ssr';
// import { findUserById } from '@/api/users';
import { tableNames } from '@/constants/table_names';
// import {
//   checkDuplicate,
//   createInvoice,
//   linkInvoiceWithSlpNoteQuickBooksP,
//   insertIntoClients,
//   updateClient,
//   updateInvoice,
// } from '@/reuseables/invoice/helpers';
import { createClient } from '@supabase/supabase-js';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { createBaseInvoice, updateInvoiceById } from '@/app/service/invoice';
import {
  getTaxValueForAnItem,
  handleInvoiceItems,
} from '../(package-structure)/newsf/invoice/util';
import { getServiceById } from '@/app/service/service';
import { updateClientById } from '@/app/service/client';
import { updateActivities } from './utils';
// import { addTimeToDate } from '@/utils/date-formatter';

// const supabase = createClient(env.SUPABASE_URL!, env.SUPABASE_ANON_KEY!);

const supabase = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
export async function POST(req: NextRequest) {
  const body = await req.json();
  console.log('BODY LOGGED BY AYO IS', body, 'ENDS HERE');

  try {
    const data_to_insert: any = {}; // Placeholder for the invoice record to be inserted/updated

    if (
      !body?.booking_id ||
      !body?.client_id ||
      !body?.organization_id ||
      !body?.slp_id
    ) {
      return NextResponse.json(
        { success: false, message: 'Missing required invoice fields.' },
        { status: 400 }
      );
    }
    // Start by adding to the the invoices table
    data_to_insert.name = body.name;
    data_to_insert.email = body.email;
    // data_to_insert.product = body.product;
    data_to_insert.service_id = body.service_id;
    data_to_insert.due_date = body.due_date;
    data_to_insert.total_price = body.total_price;
    data_to_insert.memo = body.memo;
    data_to_insert.invoice_number = body.invoice_number;
    data_to_insert.qty = body.qty;
    data_to_insert.data_source = 'manual';
    data_to_insert.slp_id = body.slp_id;
    data_to_insert.duration = body.duration;
    data_to_insert.interval = body.interval;
    data_to_insert.session_type = body.session_type;
    data_to_insert.status = body.status || 'ACTIVE';
    data_to_insert.total_hours = body.total_hours;
    data_to_insert.client_id = body.client_id;
    data_to_insert.organization_id = body.organization_id;
    data_to_insert.referral = body.referral;
    data_to_insert.package = body.package;
    data_to_insert.package_size = body.package_size;
    data_to_insert.package_id = body.package_id;
    data_to_insert.purchased_package_id = body.purchased_package_id;
    data_to_insert.invoice_date_raw = body.invoice_date;
    data_to_insert.invoice_date = new Date(body.invoice_date).toISOString();

    // 1. Create invoices with immediate info
    const baseInvoice: any = await createBaseInvoice(data_to_insert, supabase);

    const service = await getServiceById(Number(body?.service_id), supabase);

    const dummyItem = {
      product_name: service?.name,
      quantity: body?.qty,
      price: service?.price,
      service_id: body?.service_id,
      description: service?.description,
      tax_ids: service?.tax_ids,
      taxes: service?.tax_ids?.map((item: any) => ({ id: item, label: '' })),
    };

    //2.  Create invoice items
    const invoiceTotal = await handleInvoiceItems(
      baseInvoice.id,
      [dummyItem],
      supabase,
      {
        user_id: body.slp_id,
        organization_id: body?.organization_id,
        client_id: body.client_id,
        purchaseStatus: 'REDEEMED',
        booking_id: body?.booking_id,
      }
    );

    // 3. Update the invoice with the total price
    const tax = await getTaxValueForAnItem(dummyItem, supabase);

    await updateInvoiceById(
      baseInvoice.id,
      { total_price: invoiceTotal, tax_value: tax },
      supabase
    );

    // 4. Update the client with the total price
    await updateClientById(
      body?.client_id,
      {
        stage: 'Customer',
        slp_notes: 'Active',
        active_slp: Number(body?.slp_id),
      },
      supabase
    );

    // 5. Status of the entry in the followups table with the given id changed to "Complete" when an invoice is created
    const { error: followupsError } = await supabase
      .from(tableNames.followups)
      .update({ status: 'Completed' })
      .eq('client_id', data_to_insert.client_id)
      .eq('status', 'Incomplete');

    if (followupsError) throw followupsError;

    // 6. Update the soap note
    await supabase
      .from(tableNames.slp_notes)
      .update({ status: 'Created' })
      .eq('id', Number(body?.soap_note_id));

    //7. Update activities
    await updateActivities(body?.booking, body?.status, supabase);

    // 8. update bookings

    // await linkInvoiceWithSlpNoteQuickBooksP(
    //   updatedInvoice,
    //   body?.soap_note_id,
    //   supabase
    // );
    return NextResponse.json({ ...data_to_insert, invoice_id: baseInvoice.id });
  } catch (error: any) {
    console.log(error);
    return NextResponse.json({ message: error.message });
  }
}
