import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const createProduct = async (body: any) => {
  const response = await fetch(`/api/products`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error updating invoices data');
  }
  return response.json();
};

type QueryFnType = typeof createProduct;

export const useCreateProductMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },

    retry: false,
    mutationKey: ['create-product'],
    mutationFn: createProduct,
    ...config,
  });
};
