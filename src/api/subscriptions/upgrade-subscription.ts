import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const upgradeSubscription = async (body: {
  organization_id: string;
  payload: { plan: string };
}) => {
  const response = await fetch(`/api/subscription-plans/upgrade`, {
    method: 'POST',
    body: JSON.stringify(body),
  });

  if (!response.ok) throw new Error('Error updating invoice');
  return response.json();
};

type QueryFnType = typeof upgradeSubscription;
export const useUpgradeSubscriptionMutation = (
  config?: MutationConfig<QueryFnType> & { disableToast?: boolean }
) => {
  return useMutation({
    onError: (error) => {
      toaster.create({
        type: 'error',
        description: error.message || ToastMessages.somethingWrong,
      });
    },

    retry: false,
    mutationKey: ['upgrade-subscription'],
    mutationFn: upgradeSubscription,
    ...config,
  });
};
