//OPTIMIZED
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { queryKey } from '@/constants/query-key';

export async function getRedeemedSession(id: string) {
  const response = await fetch(`/api/redeemed-sessions/${id}`, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch services');
  }
  return await response.json();
}

type QueryFnType = typeof getRedeemedSession;

export const useGetRedeemedSessionQuery = (
  id: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.redeemedSession.getAll, Number(id)],
    queryFn: () => getRedeemedSession(id),
    ...config,
  });
};
