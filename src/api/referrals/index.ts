import { tableNames } from '@/constants/table_names';
import { QueryConfigType } from '@/lib/react-query';
import supabase from '@/lib/supabase/client';
import { useQuery } from '@supabase-cache-helpers/postgrest-react-query';

import 'moment-timezone';
import 'moment/locale/en-ca';

export function fetchReferralCredits(id: number) {
  return supabase
    .from(tableNames.referrals)
    .select(`*`, { count: 'exact', head: true })
    .eq('referrer_id', id)
    .is('referee_invoice', null)
    .is('referrer_invoice', null);
}
export function fetchRClientferralCredits(refId: number, reeId: number) {
  return supabase
    .from(tableNames.referrals)
    .select(`*`, { count: 'exact', head: true })
    .eq('referrer_id', refId)
    .eq('referee_id', reeId)
    .is('referee_invoice', null)
    .is('referrer_invoice', null);
}
export function fetchTotalReferrals(id: number) {
  return supabase
    .from(tableNames.referrals)
    .select(`*`, { count: 'exact', head: true })
    .eq('referrer_id', id);
}
export function fetchReferrals(id: number) {
  return supabase
    .from(tableNames.referrals)
    .select(
      `*, referrer:clients!referrer_id (*), referee:clients!referee_id (*)`
    )
    .eq('referee_id', id);
}

type QueryFnType = typeof fetchReferralCredits;

export const useGetClientReferralCreditsApi = (
  refId: number,
  reeId: number,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery(fetchRClientferralCredits(refId, reeId), config);
};
export const useGetReferralCreditsApi = (
  id: number,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery(fetchReferralCredits(id), config);
};

export const useGetTotalReferralsApi = (
  id: number,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery(fetchTotalReferrals(id), config);
};

export const useGetReferralsApi = (
  id: number,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery(fetchReferrals(id), config);
};
