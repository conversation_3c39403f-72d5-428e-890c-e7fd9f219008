import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';
import { QueryConfigType } from '@/lib/react-query';
import { useQuery } from '@supabase-cache-helpers/postgrest-react-query';
import { IUserPersmissionState } from '@/shared/interface/user';

export const getPermissionsForUser = (user: Partial<IUserPersmissionState>) => {
  const response = supabase
    .from(tableNames.user_permissions)
    .select(`*`)
    .eq('user_id', user);
  return response;
};

type QueryFnType = typeof getPermissionsForUser;

export const useGetPermissionsForUser = (
  user: IUserPersmissionState,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery(getPermissionsForUser(user), config);
};
