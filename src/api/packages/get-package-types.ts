//OPTIMIZED
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

export async function getPackageTypes() {
  const response = await fetch('/api/package-type', {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch package types');
  }
  const data = await response.json();
  return data;
}

type QueryFnType = typeof getPackageTypes;

export const useGetPackageTypesQuery = (
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: ['get-all-packages-types'],
    queryFn: getPackageTypes,
    ...config,
  });
};
