//OPTIMIZED
import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';

const updateRedeemedSession = async (body: { data: any; id: number }) => {
  const response = await fetch(`/api/redeemed-sessions/${body.id}`, {
    method: 'PATCH',
    body: JSON.stringify(body.data),
  });
  if (!response.ok) throw new Error('Error updating Session');
  return response.json();
};

type QueryFnType = typeof updateRedeemedSession;

export const useUpdateRedeemedSessionMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    retry: false,
    mutationKey: [queryKey.redeemSession.updateRedeemedSession],
    mutationFn: updateRedeemedSession,
    ...config,
  });
};
