/* eslint-disable no-constant-condition */
import supabase from '@/lib/supabase/client';
import { tableNames } from '@/constants/table_names';

export const findExistingClientByInitialEmail = async (
  email: string,
  supabase: any
) => {
  // Query the 'clients' table for entries with the provided initial email
  const { data, error } = await supabase
    .from(tableNames.clients)
    .select(`*`)
    .eq('initial_email', email);
  if (error) throw error;
  return data[0];
};

export const createNewClient = async (clientObj: any, supabase: any) => {
  if (!clientObj.organization_id) throw new Error('Missing organization_id');

  const { data, error } = await supabase
    .from(tableNames.clients)
    .insert(clientObj)
    .select();
  if (error) throw error;

  return data[0];
};

export const findAllClients = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.clients)
      .select('*')
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }

    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};
export const findAllWaitlistClients = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from(tableNames.clients)
      .select('*')
      .not('waitlist', 'is', null)
      .neq('stage', 'Customer')
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }

    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};
