import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const createPackageOffering = async (body: any) => {
  const response = await fetch(`/api/package-offering`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error creating package offering');
  }
  return response.json();
};
type MutationFnType = typeof createPackageOffering;

export const useCreatePackageOfferingMutation = (
  config?: MutationConfig<MutationFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    retry: false,
    mutationKey: ['create-package-offering'],
    mutationFn: createPackageOffering,
    ...config,
  });
};
