import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const updateReferralCredit = async (body: any) => {
  const response = await fetch(`/api/referrals/update-referrals`, {
    method: 'POST',
    body: JSON.stringify(body),
  });

  if (!response.ok) throw new Error('Error updating invoice');
  return response.json();
};

type QueryFnType = typeof updateReferralCredit;
export const useUpdateReferralCreditMutation = (
  config?: MutationConfig<QueryFnType> & { disableToast?: boolean }
) => {
  return useMutation({
    onError: () => {
      if (!config?.disableToast) {
        toaster.create({ type: 'error', description: 'An error occurred' });
      }
    },
    onSuccess: () => {
      if (!config?.disableToast) {
        toaster.create({
          type: 'success',
          description: ToastMessages.referrals.updateSuccess,
        });
      }
    },
    retry: false,
    mutationKey: ['update-referral-credit'],
    mutationFn: updateReferralCredit,
    ...config,
  });
};
