import { queryKey } from '@/constants/query-key';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

async function getInvoicesBySlpId(payload: {
  id: number;
  organization_id: number;
}) {
  const baseUrl = '/api/invoices/user';
  const apiUrl = buildUrlWithQueryParams(baseUrl, { ...payload });

  const response = await fetch(apiUrl, {
    method: 'GET',
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to fetch statistics');
  }
  const data = await response.json();
  return data;
}

type QueryFnType = typeof getInvoicesBySlpId;

export const useFindSlpInvoiceChartApi = (
  data: { id: number; organization_id: number },
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.slp.getStatistics, data],
    queryFn: () => getInvoicesBySlpId(data),
    ...config,
  });
};
