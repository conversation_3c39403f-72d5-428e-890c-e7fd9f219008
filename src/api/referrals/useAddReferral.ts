import { MutationConfig, useMutation } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';

const addReferral = async (body: any) => {
  const response = await fetch(`/api/referrals`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) throw new Error('Error updating invoice');
  return response.json();
};

type QueryFnType = typeof addReferral;

export const useAddReferralMutation = (
  config?: MutationConfig<QueryFnType>
) => {
  return useMutation({
    onError: () => {
      toaster.create({ type: 'error', description: 'An error occurred' });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: ToastMessages.referrals.updateSuccess,
      });
    },
    retry: false,
    mutationKey: ['update-referral'],
    mutationFn: addReferral,
    ...config,
  });
};
